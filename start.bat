@echo off
echo ========================================
echo    OSI模型可视化系统启动脚本
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js环境检查通过
echo.

echo 正在检查项目依赖...
if not exist "node_modules" (
    echo 未检测到node_modules文件夹，正在安装依赖...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo 依赖安装失败，请检查网络连接或尝试使用yarn
        pause
        exit /b 1
    )
    echo 依赖安装完成！
) else (
    echo 依赖已存在，跳过安装步骤
)

echo.
echo 正在启动开发服务器...
echo 请稍等，浏览器将自动打开 http://localhost:3000
echo.
echo 如需停止服务器，请按 Ctrl+C
echo ========================================
echo.

npm start
